# 手机号验证命令使用指南

本文档介绍如何使用Laravel Artisan命令来验证和管理Phone模型中的手机号码。

## 命令概览

### 1. `phone:validate` - 验证手机号是否失效

这个命令通过实际发送验证码请求来检测手机号是否仍然有效。

#### 基本用法

```bash
# 验证5个正常状态的手机号（仅显示结果，不更新数据库）
php artisan phone:validate --dry-run --limit=5

# 验证并更新失效手机号的状态
php artisan phone:validate --update --limit=10

# 验证特定状态的手机号
php artisan phone:validate --status=normal --limit=20

# 设置验证超时时间
php artisan phone:validate --timeout=15 --limit=10
```

#### 参数说明

- `--status=normal` : 验证指定状态的手机号
  - `normal` : 正常状态（默认）
  - `invalid` : 失效状态
  - `bound` : 已绑定状态
  - `Binding` : 绑定中状态

- `--limit=50` : 每次验证的手机号数量（默认50）

- `--timeout=10` : 每个手机号的验证超时时间，单位秒（默认10秒）

- `--update` : 是否更新失效手机号的状态为`invalid`

- `--dry-run` : 仅显示验证结果，不更新数据库

- `-v` : 显示详细输出，包括每个手机号的验证结果

#### 验证逻辑

命令通过以下方式判断手机号是否失效：

1. **调用手机号的验证接口**：使用`Phone::makePhoneRequest()`方法
2. **检查响应代码**：
   - `code: 10022` → 手机号失效
   - HTTP成功响应 → 手机号有效
   - 其他HTTP错误 → 验证出错
3. **异常处理**：捕获`InvalidPhoneException`等异常

#### 输出示例

```
开始验证手机号状态...
验证状态: normal
验证数量: 5
超时时间: 10秒
运行在 DRY-RUN 模式，不会更新数据库
找到 5 个手机号需要验证

❌ +18022132960 - 失效: 手机号失效 (code: 10022)
❌ +16262491414 - 失效: 手机号失效 (code: 10022)
✅ +17403798897 - 有效

验证完成！
+------+------+
| 状态 | 数量 |
+------+------+
| 有效 | 1    |
| 失效 | 4    |
| 错误 | 0    |
| 总计 | 5    |
+------+------+

数据库统计信息:
+--------+------+
| 状态   | 数量 |
+--------+------+
| 正常   | 363  |
| 失效   | 12   |
| 已绑定 | 5    |
| 绑定中 | 0    |
+--------+------+
```

### 2. `phone:report` - 生成手机号验证报告

这个命令生成详细的手机号统计报告，包括状态分布、国家分布等信息。

#### 基本用法

```bash
# 生成最近30天的报告
php artisan phone:report

# 生成最近7天的报告
php artisan phone:report --days=7

# 按国家筛选
php artisan phone:report --country=US

# 导出为CSV格式
php artisan phone:report --export=csv

# 导出为JSON格式
php artisan phone:report --export=json
```

#### 参数说明

- `--days=30` : 显示最近N天的数据（默认30天）
- `--country=` : 按国家代码筛选（如：US、CN等）
- `--export=` : 导出报告到文件
  - `csv` : 导出为CSV格式
  - `json` : 导出为JSON格式

#### 报告内容

1. **总体统计**：总数量、失效数量、失效率
2. **按状态统计**：各状态的数量和占比
3. **按国家统计**：前10个国家的分布情况
4. **推荐操作**：基于失效率的建议

#### 输出示例

```
📊 手机号验证报告
生成时间: 2025-07-25 20:01:03

📈 总体统计
+----------+-------+
| 指标     | 数值  |
+----------+-------+
| 总数量   | 380   |
| 失效数量 | 12    |
| 失效率   | 3.16% |
+----------+-------+

📋 按状态统计
+--------+------+--------+
| 状态   | 数量 | 占比   |
+--------+------+--------+
| 正常   | 363  | 95.53% |
| 失效   | 12   | 3.16%  |
| 已绑定 | 5    | 1.32%  |
| 绑定中 | 0    | 0%     |
+--------+------+--------+

💡 推荐操作
失效率正常 (3.16%)
```

## 使用场景

### 1. 定期验证手机号有效性

```bash
# 每天验证100个正常状态的手机号
php artisan phone:validate --status=normal --limit=100 --update
```

### 2. 批量清理失效手机号

```bash
# 先验证不更新数据库
php artisan phone:validate --dry-run --limit=200 -v

# 确认无误后更新数据库
php artisan phone:validate --update --limit=200
```

### 3. 监控手机号质量

```bash
# 生成周报
php artisan phone:report --days=7 --export=csv

# 生成月报
php artisan phone:report --days=30 --export=json
```

### 4. 调试特定手机号

```bash
# 验证单个手机号（设置limit=1）
php artisan phone:validate --limit=1 --dry-run -v
```

## 注意事项

1. **验证频率**：避免过于频繁的验证，以免触发API限制
2. **超时设置**：根据网络情况调整`--timeout`参数
3. **批量大小**：建议`--limit`不要设置过大，避免长时间运行
4. **日志记录**：所有验证结果都会记录到Laravel日志中
5. **备份数据**：在使用`--update`选项前，建议先使用`--dry-run`确认结果

## 定时任务配置

可以在`routes/console.php`中配置定时任务：

```php
use Illuminate\Support\Facades\Schedule;

// 每天凌晨2点验证100个手机号
Schedule::command('phone:validate --update --limit=100')
    ->dailyAt('02:00')
    ->withoutOverlapping();

// 每周一生成报告
Schedule::command('phone:report --export=csv')
    ->weeklyOn(1, '09:00');
```

## 故障排除

### 1. 验证失败率过高

- 检查网络连接
- 调整超时时间
- 检查API配额

### 2. 命令执行缓慢

- 减少`--limit`参数
- 增加`--timeout`参数
- 检查数据库性能

### 3. 内存不足

- 减少批量处理大小
- 检查PHP内存限制

通过这些命令，你可以有效地管理和监控Phone模型中的手机号码质量。
