/.phpunit.cache
/node_modules
/public/build
/public/hot
/public/storage
/public/.well-known/
public/vendor/
/storage/*.key
/vendor
.env
.env.backup
.env.production
.phpactor.json
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json
npm-debug.log
yarn-error.log
/.fleet
/.idea
/.vscode
.well-known/
.user.ini

/storage/framework/views/
/storage/browser/screenshots/


example-apple.code-workspace
appleid-batch-registration.code-workspace
.cursor

# Todo2 - AI-powered task management directories
.cursor/rules/todo2-overview.mdc
.cursor/rules/todo2.mdc
.cursor/mcp.json
.todo2/
