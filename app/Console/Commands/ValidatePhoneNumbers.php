<?php

namespace App\Console\Commands;

use App\Models\Phone;
use App\Services\Integrations\Phone\Exception\InvalidPhoneException;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ValidatePhoneNumbers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'phone:validate
                            {--status=normal : 验证指定状态的手机号}
                            {--limit=50 : 每次验证的手机号数量}
                            {--timeout=10 : 每个手机号的验证超时时间}
                            {--update : 是否更新失效手机号的状态}
                            {--dry-run : 仅显示结果不更新数据库}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Validate phone numbers to check if they are still active';

    private int $validCount = 0;
    private int $invalidCount = 0;
    private int $errorCount = 0;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $status = $this->option('status');
        $limit = (int) $this->option('limit');
        $timeout = (int) $this->option('timeout');
        $shouldUpdate = $this->option('update');
        $dryRun = $this->option('dry-run');

        $this->info("开始验证手机号状态...");
        $this->info("验证状态: {$status}");
        $this->info("验证数量: {$limit}");
        $this->info("超时时间: {$timeout}秒");

        if ($dryRun) {
            $this->warn("运行在 DRY-RUN 模式，不会更新数据库");
        }

        // 获取需要验证的手机号
        $phones = Phone::where('status', $status)
            ->whereNotNull('phone_address')
            ->limit($limit)
            ->get();

        if ($phones->isEmpty()) {
            $this->warn("没有找到状态为 '{$status}' 的手机号");
            return;
        }

        $this->info("找到 {$phones->count()} 个手机号需要验证");

        $progressBar = $this->output->createProgressBar($phones->count());
        $progressBar->start();

        foreach ($phones as $phone) {
            $this->validatePhone($phone, $timeout, $shouldUpdate, $dryRun);
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        $this->displayResults();
    }

    /**
     * 验证单个手机号
     */
    private function validatePhone(Phone $phone, int $timeout, bool $shouldUpdate, bool $dryRun): void
    {
        try {
            $phoneRequest = $phone->makePhoneRequest();

            // 设置超时时间
            $phoneRequest->config()->add('timeout', $timeout);

            // 尝试获取验证码来验证手机号是否有效
            $response = $phoneRequest->send();

            if ($response->json('code') === 10022) {
                $this->handleInvalidPhone($phone, '手机号失效 (code: 10022)', $shouldUpdate, $dryRun);
            } elseif ($response->successful()) {
                $this->handleValidPhone($phone, $shouldUpdate, $dryRun);
            } else {
                $this->handleErrorPhone($phone, "HTTP错误: {$response->status()}", $shouldUpdate, $dryRun);
            }
        } catch (InvalidPhoneException $e) {
            $this->handleInvalidPhone($phone, $e->getMessage(), $shouldUpdate, $dryRun);
        } catch (\Exception $e) {
            $this->handleErrorPhone($phone, $e->getMessage(), $shouldUpdate, $dryRun);
        }
    }

    /**
     * 处理有效的手机号
     */
    private function handleValidPhone(Phone $phone, bool $shouldUpdate, bool $dryRun): void
    {
        $this->validCount++;

        if ($this->output->isVerbose()) {
            $this->line("✅ {$phone->phone} - 有效");
        }

        Log::info("手机号验证成功", [
            'phone_id' => $phone->id,
            'phone' => $phone->phone,
            'status' => '有效'
        ]);
    }

    /**
     * 处理失效的手机号
     */
    private function handleInvalidPhone(Phone $phone, string $reason, bool $shouldUpdate, bool $dryRun): void
    {
        $this->invalidCount++;

        if ($this->output->isVerbose()) {
            $this->line("❌ {$phone->phone} - 失效: {$reason}");
        }

        if ($shouldUpdate && !$dryRun) {
            $phone->update(['status' => Phone::STATUS_INVALID]);
        }

        Log::warning("手机号验证失败", [
            'phone_id' => $phone->id,
            'phone' => $phone->phone,
            'reason' => $reason,
            'updated' => $shouldUpdate && !$dryRun
        ]);
    }

    /**
     * 处理验证出错的手机号
     */
    private function handleErrorPhone(Phone $phone, string $error, bool $shouldUpdate, bool $dryRun): void
    {
        $this->errorCount++;

        if ($this->output->isVerbose()) {
            $this->line("⚠️  {$phone->phone} - 错误: {$error}");
        }

        Log::error("手机号验证出错", [
            'phone_id' => $phone->id,
            'phone' => $phone->phone,
            'error' => $error
        ]);
    }

    /**
     * 显示验证结果
     */
    private function displayResults(): void
    {
        $this->info("验证完成！");
        $this->table(
            ['状态', '数量'],
            [
                ['有效', $this->validCount],
                ['失效', $this->invalidCount],
                ['错误', $this->errorCount],
                ['总计', $this->validCount + $this->invalidCount + $this->errorCount]
            ]
        );

        if ($this->invalidCount > 0) {
            $this->warn("发现 {$this->invalidCount} 个失效的手机号");

            if (!$this->option('update')) {
                $this->info("使用 --update 选项来更新失效手机号的状态");
            }
        }

        if ($this->errorCount > 0) {
            $this->error("有 {$this->errorCount} 个手机号验证时出现错误，请检查日志");
        }

        // 显示数据库统计信息
        $this->displayDatabaseStats();
    }

    /**
     * 显示数据库中手机号的统计信息
     */
    private function displayDatabaseStats(): void
    {
        $this->newLine();
        $this->info("数据库统计信息:");

        $stats = DB::table('phone')
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status')
            ->toArray();

        $tableData = [];
        foreach (Phone::STATUS as $status => $label) {
            $count = $stats[$status] ?? 0;
            $tableData[] = [$label, $count];
        }

        $this->table(['状态', '数量'], $tableData);
    }
}
