<?php

namespace App\Console\Commands;

use App\Models\Phone;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PhoneReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'phone:report 
                            {--export= : 导出报告到文件}
                            {--country= : 按国家筛选}
                            {--days=30 : 显示最近N天的数据}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate phone number validation report';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $exportFormat = $this->option('export');
        $country = $this->option('country');
        $days = (int) $this->option('days');

        $this->info("生成手机号验证报告...");
        
        if ($country) {
            $this->info("筛选国家: {$country}");
        }
        
        $this->info("时间范围: 最近 {$days} 天");

        // 获取基础统计数据
        $stats = $this->getPhoneStats($country, $days);
        
        // 显示报告
        $this->displayReport($stats);
        
        // 导出报告
        if ($exportFormat) {
            $this->exportReport($stats, $exportFormat);
        }
    }

    /**
     * 获取手机号统计数据
     */
    private function getPhoneStats(?string $country, int $days): array
    {
        $query = Phone::query();
        
        if ($country) {
            $query->where('country_code', $country);
        }
        
        $query->where('created_at', '>=', Carbon::now()->subDays($days));

        // 按状态统计
        $statusStats = $query->clone()
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status')
            ->toArray();

        // 按国家统计
        $countryStats = $query->clone()
            ->select('country_code', DB::raw('count(*) as count'))
            ->groupBy('country_code')
            ->orderByDesc('count')
            ->limit(10)
            ->get()
            ->toArray();

        // 失效率统计
        $totalCount = array_sum($statusStats);
        $invalidCount = $statusStats[Phone::STATUS_INVALID] ?? 0;
        $invalidRate = $totalCount > 0 ? round(($invalidCount / $totalCount) * 100, 2) : 0;

        return [
            'status_stats' => $statusStats,
            'country_stats' => $countryStats,
            'total_count' => $totalCount,
            'invalid_count' => $invalidCount,
            'invalid_rate' => $invalidRate,
            'generated_at' => Carbon::now()->toDateTimeString(),
            'filter_country' => $country,
            'filter_days' => $days
        ];
    }

    /**
     * 显示报告
     */
    private function displayReport(array $stats): void
    {
        $this->newLine();
        $this->info("📊 手机号验证报告");
        $this->info("生成时间: {$stats['generated_at']}");
        $this->newLine();

        // 总体统计
        $this->info("📈 总体统计");
        $this->table(
            ['指标', '数值'],
            [
                ['总数量', $stats['total_count']],
                ['失效数量', $stats['invalid_count']],
                ['失效率', $stats['invalid_rate'] . '%']
            ]
        );

        // 按状态统计
        $this->newLine();
        $this->info("📋 按状态统计");
        $statusTableData = [];
        $statusConstants = [
            Phone::STATUS_NORMAL => '正常',
            Phone::STATUS_INVALID => '失效', 
            Phone::STATUS_BOUND => '已绑定',
            Phone::STATUS_BINDING => '绑定中'
        ];
        foreach ($statusConstants as $status => $label) {
            $count = $stats['status_stats'][$status] ?? 0;
            $percentage = $stats['total_count'] > 0 ? 
                round(($count / $stats['total_count']) * 100, 2) : 0;
            $statusTableData[] = [$label, $count, $percentage . '%'];
        }
        $this->table(['状态', '数量', '占比'], $statusTableData);

        // 按国家统计（前10）
        if (!empty($stats['country_stats'])) {
            $this->newLine();
            $this->info("🌍 按国家统计 (前10)");
            $countryTableData = [];
            foreach ($stats['country_stats'] as $countryStat) {
                $percentage = $stats['total_count'] > 0 ? 
                    round(($countryStat['count'] / $stats['total_count']) * 100, 2) : 0;
                $countryTableData[] = [
                    $countryStat['country_code'], 
                    $countryStat['count'], 
                    $percentage . '%'
                ];
            }
            $this->table(['国家代码', '数量', '占比'], $countryTableData);
        }

        // 推荐操作
        $this->newLine();
        $this->info("💡 推荐操作");
        if ($stats['invalid_rate'] > 10) {
            $this->warn("失效率较高 ({$stats['invalid_rate']}%)，建议:");
            $this->line("  • 运行 php artisan phone:validate --update 更新失效状态");
            $this->line("  • 检查手机号来源质量");
        } else {
            $this->info("失效率正常 ({$stats['invalid_rate']}%)");
        }
    }

    /**
     * 导出报告
     */
    private function exportReport(array $stats, string $format): void
    {
        $filename = "phone_report_" . date('Y-m-d_H-i-s') . ".{$format}";
        $filepath = storage_path("app/reports/{$filename}");
        
        // 确保目录存在
        if (!is_dir(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        switch ($format) {
            case 'csv':
                $this->exportToCsv($stats, $filepath);
                break;
            case 'json':
                $this->exportToJson($stats, $filepath);
                break;
            default:
                $this->error("不支持的导出格式: {$format}");
                return;
        }

        $this->info("报告已导出到: {$filepath}");
    }

    /**
     * 导出为CSV格式
     */
    private function exportToCsv(array $stats, string $filepath): void
    {
        $handle = fopen($filepath, 'w');
        
        // 写入BOM以支持中文
        fwrite($handle, "\xEF\xBB\xBF");
        
        // 写入标题
        fputcsv($handle, ['手机号验证报告']);
        fputcsv($handle, ['生成时间', $stats['generated_at']]);
        fputcsv($handle, []);
        
        // 写入状态统计
        fputcsv($handle, ['状态统计']);
        fputcsv($handle, ['状态', '数量']);
        $statusConstants = [
            Phone::STATUS_NORMAL => '正常',
            Phone::STATUS_INVALID => '失效', 
            Phone::STATUS_BOUND => '已绑定',
            Phone::STATUS_BINDING => '绑定中'
        ];
        foreach ($statusConstants as $status => $label) {
            $count = $stats['status_stats'][$status] ?? 0;
            fputcsv($handle, [$label, $count]);
        }
        
        fclose($handle);
    }

    /**
     * 导出为JSON格式
     */
    private function exportToJson(array $stats, string $filepath): void
    {
        file_put_contents($filepath, json_encode($stats, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
}
