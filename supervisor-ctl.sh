#!/bin/bash

# Laravel Sail Supervisor Control Script
# 用法: ./supervisor-ctl.sh [command]

if [ $# -eq 0 ]; then
    echo "🔧 Laravel Sail Supervisor Control"
    echo
    echo "用法: $0 [command]"
    echo
    echo "可用命令:"
    echo "  status    - 显示所有服务状态"
    echo "  start     - 启动所有服务"
    echo "  stop      - 停止所有服务"
    echo "  restart   - 重启所有服务"
    echo "  logs      - 显示 Horizon 日志"
    echo "  horizon   - 检查 Horizon 状态"
    echo "  reread    - 重新读取配置"
    echo "  update    - 更新配置并重启服务"
    echo
    exit 1
fi

SUPERVISOR_CMD="./vendor/bin/sail exec laravel.test supervisorctl -c /etc/supervisor/conf.d/supervisord.conf"

case "$1" in
    "status")
        echo "📊 检查所有服务状态..."
        $SUPERVISOR_CMD status
        ;;
    "start")
        echo "🚀 启动所有服务..."
        $SUPERVISOR_CMD start all
        ;;
    "stop")
        echo "🛑 停止所有服务..."
        $SUPERVISOR_CMD stop all
        ;;
    "restart")
        echo "🔄 重启所有服务..."
        $SUPERVISOR_CMD restart all
        ;;
    "logs")
        echo "📝 显示 Horizon 日志 (Ctrl+C 退出)..."
        ./vendor/bin/sail exec laravel.test tail -f /var/www/html/storage/logs/horizon.log
        ;;
    "horizon")
        echo "🎯 检查 Horizon 状态..."
        ./vendor/bin/sail artisan horizon:status
        ;;
    "reread")
        echo "📚 重新读取配置..."
        $SUPERVISOR_CMD reread
        $SUPERVISOR_CMD update
        ;;
    "update")
        echo "🔄 更新配置并重启服务..."
        $SUPERVISOR_CMD reread
        $SUPERVISOR_CMD update
        $SUPERVISOR_CMD restart all
        ;;
    *)
        echo "❌ 未知命令: $1"
        echo "运行 '$0' 查看可用命令"
        exit 1
        ;;
esac 